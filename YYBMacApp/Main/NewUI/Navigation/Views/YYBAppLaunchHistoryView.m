//
//  YYBAppLaunchHistoryView.m
//  YYBMacApp
//
//  Created by lichenlin on 2025/8/20.
//

#import "YYBAppLaunchHistoryView.h"
#import "InstallApkInfo.h"
#import "SDWebImage.h"
#import <YYBMacFusionSDK/YYBMacLog.h>
#import "Masonry.h"
#import "YYBApkPackage.h"


static const CGFloat kItemHeight = 40.0;  // 每个展开的条目高度
static const CGFloat kItemSpacing = 8.0;  // 每个条目之间间距
static const CGFloat kIconSize = 28.0;  // 图标大小
static const CGFloat kFixedHeight = 164.0;  // view固定高度
static const CGFloat kTextLabelSpacing = 16.0;  // 文本标签与app item间隔


static NSString *const kTag = @"YYBAppLaunchHistoryView";

// 自定义的可点击 item view
@interface YYBAppLaunchHistoryItemView : NSView
@property (nonatomic, strong) InstallApkInfo *apkInfo;
@property (nonatomic, assign) BOOL expanded;
@end

@implementation YYBAppLaunchHistoryItemView

- (instancetype)initWithFrame:(NSRect)frameRect {
    self = [super initWithFrame:frameRect];
    if (self) {
        self.wantsLayer = YES;
        self.layer.backgroundColor = [NSColor clearColor].CGColor;
        self.layer.cornerRadius = 8.0;
        self.layer.masksToBounds = YES;

        // 添加鼠标跟踪区域
        NSTrackingArea *trackingArea = [[NSTrackingArea alloc] initWithRect:self.bounds
                                                                    options:NSTrackingMouseEnteredAndExited | NSTrackingActiveInKeyWindow | NSTrackingInVisibleRect
                                                                      owner:self
                                                                   userInfo:nil];
        [self addTrackingArea:trackingArea];
    }
    return self;
}

- (void)mouseEntered:(NSEvent *)event {
    // Hover 效果：border-radius: 8px; background: rgba(255, 255, 255, 0.12);
    self.layer.backgroundColor = [[NSColor colorWithWhite:1.0 alpha:0.12] CGColor];
}

- (void)mouseExited:(NSEvent *)event {
    // 恢复透明背景
    self.layer.backgroundColor = [NSColor clearColor].CGColor;
}

- (void)mouseDown:(NSEvent *)event {
    // 点击效果
    if (self.apkInfo && self.apkInfo.pkgName) {
        YYBMacLogInfo(kTag, @"点击打开APP: %@", self.apkInfo.name);
        [[YYBApkPackage shared] openApp:self.apkInfo.pkgName];
    }
}

@end

@interface YYBAppLaunchHistoryView ()

@property (nonatomic, strong) NSTextField *titleLabel;  // 标题标签
@property (nonatomic, strong) NSMutableArray<YYBAppLaunchHistoryItemView *> *itemViews;

@end


@implementation YYBAppLaunchHistoryView


- (instancetype)initWithFrame:(NSRect)frameRect {
    self = [super initWithFrame:frameRect];
    if (self) {
        _expanded = YES; // 默认展开状态
        _itemViews = [NSMutableArray array];
        [self setupView];
    }
    return self;
}


- (void)setupView {
    self.wantsLayer = YES;
    self.layer.backgroundColor = [NSColor clearColor].CGColor;

    [self setupTitleLabel];
    [self updateLayout];
}

- (void)setupTitleLabel {
    self.titleLabel = [[NSTextField alloc] init];
    self.titleLabel.stringValue = self.expanded ? @"快捷启动" : @"启动";
    self.titleLabel.font = [NSFont systemFontOfSize:12 weight:NSFontWeightRegular];
    self.titleLabel.textColor = [[NSColor whiteColor] colorWithAlphaComponent:0.45];
    self.titleLabel.backgroundColor = [NSColor clearColor];
    self.titleLabel.bordered = NO;
    self.titleLabel.editable = NO;
    self.titleLabel.selectable = NO;
    self.titleLabel.alignment = NSTextAlignmentCenter;

    [self addSubview:self.titleLabel];
}


// 切换展开和收起
- (void)setExpanded:(BOOL)expanded {
    if (_expanded != expanded) {
        _expanded = expanded;
        // 更新标题文本
        self.titleLabel.stringValue = self.expanded ? @"快捷启动" : @"启动";
        // 重新布局、更新界面
        [self updateLayout];
    }
}

- (void)updateWithRecentApps:(NSArray<InstallApkInfo *> *)recentApps {
    // 清除旧的视图
    for (NSView *view in self.itemViews) {
        [view removeFromSuperview];
    }
    [self.itemViews removeAllObjects];

    // 最多显示3个
    NSInteger count = MIN(recentApps.count, 3);

    // 如果没有app item，隐藏整个view
    if (count == 0) {
        self.hidden = YES;
        [self updateLayout];
        return;
    } else {
        self.hidden = NO;
    }

    // 创建新的视图
    for (NSInteger i = 0; i < count; i++) {
        InstallApkInfo *apkInfo = recentApps[i];
        YYBMacLogInfo(kTag, @"刷新历史记录显示，获取到第 %ld 个最近使用的APP: %@", (long)(i+1), apkInfo.name);
        YYBAppLaunchHistoryItemView *itemView = [self createItemViewForApkInfo:apkInfo];

        [self addSubview:itemView];
        [self.itemViews addObject:itemView];
    }

    [self updateLayout];
}


- (YYBAppLaunchHistoryItemView *)createItemViewForApkInfo:(InstallApkInfo *)apkInfo {
    YYBAppLaunchHistoryItemView *containerView = [[YYBAppLaunchHistoryItemView alloc] init];
    containerView.apkInfo = apkInfo;
    containerView.expanded = self.expanded;


    // 创建图标
    NSImageView *iconView = [[NSImageView alloc] init];
    iconView.imageScaling = NSImageScaleProportionallyUpOrDown;
    iconView.wantsLayer = YES;
    iconView.layer.cornerRadius = 4.0;
    iconView.layer.masksToBounds = YES;
    // 设置icon图标
    [iconView sd_setImageWithURL:[NSURL URLWithString:apkInfo.iconUrl]];


    [containerView addSubview:iconView];


    // 创建应用名称标签（仅在展开状态显示）
    NSTextField *nameLabel = [[NSTextField alloc] init];
    nameLabel.stringValue = apkInfo.name ?: @"";
    nameLabel.font = [NSFont systemFontOfSize:12];
    nameLabel.textColor = [NSColor whiteColor];
    nameLabel.backgroundColor = [NSColor clearColor];
    nameLabel.bordered = NO;
    nameLabel.editable = NO;
    nameLabel.selectable = NO;
    nameLabel.lineBreakMode = NSLineBreakByTruncatingTail;
    [containerView addSubview:nameLabel];
    
    // 设置约束
    if (self.expanded) {
        [iconView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(containerView).offset(8);
            make.centerY.equalTo(containerView);
            make.width.height.equalTo(@(kIconSize));
        }];
        
        [nameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(iconView.mas_right).offset(8);
            make.right.equalTo(containerView).offset(-8);
            make.centerY.equalTo(containerView);
        }];
    } else {
        [iconView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.center.equalTo(containerView);
            
        }];
        
        nameLabel.hidden = YES;
    }
    
    return containerView;
}


- (void)updateLayout {

    if(self.itemViews.count == 0) {
        // view消失 - 只更新高度约束，不要清除其他约束。防止刷新向左偏移
        [self mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.equalTo(@0);
        }];
        return;
    }

    // 固定整个view的高度为164 - 只更新高度约束，不要清除其他约束。防止刷新向左偏移
    [self mas_updateConstraints:^(MASConstraintMaker *make) {
        make.height.equalTo(@(kFixedHeight));
    }];

    // 布局标题标签
    [self.titleLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self);
        make.top.equalTo(self);
        make.height.equalTo(@20); // 标题标签高度
    }];



    CGFloat itemHeight = self.expanded ? kItemHeight : kIconSize + 8;
    CGFloat startY = 20 + kTextLabelSpacing; // 标题高度 + 间隔

    // 布局app item，向上对齐
    for (NSInteger i = 0; i < self.itemViews.count; i++) {
        YYBAppLaunchHistoryItemView *itemView = self.itemViews[i];
        [itemView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.right.equalTo(self);
            make.height.equalTo(@(itemHeight));
            make.top.equalTo(self).offset(startY + i * (itemHeight + kItemSpacing));
        }];
    }

    [self updateItemViewsForExpandedState];
}


- (void)updateItemViewsForExpandedState {
    for (YYBAppLaunchHistoryItemView *itemView in self.itemViews) {
        itemView.expanded = self.expanded;

        NSImageView *iconView = nil;
        NSTextField *nameLabel = nil;

        for (NSView *subview in itemView.subviews) {
            if ([subview isKindOfClass:[NSImageView class]]) {
                iconView = (NSImageView *)subview;
            } else if ([subview isKindOfClass:[NSTextField class]]) {
                nameLabel = (NSTextField *)subview;
            }
        }

        if (iconView && nameLabel) {
            nameLabel.hidden = !self.expanded;

            [iconView mas_remakeConstraints:^(MASConstraintMaker *make) {
                if (self.expanded) {
                    make.left.equalTo(itemView).offset(8);
                    make.centerY.equalTo(itemView);
                    make.width.height.equalTo(@(kIconSize));
                } else {
                    make.center.equalTo(itemView);
                    make.width.height.equalTo(@(kIconSize));
                }
            }];

            if (self.expanded) {
                [nameLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
                    make.left.equalTo(iconView.mas_right).offset(8);
                    make.right.equalTo(itemView).offset(-8);
                    make.centerY.equalTo(itemView);
                }];
            }
        }
    }
}


@end
